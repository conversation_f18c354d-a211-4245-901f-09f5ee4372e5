@page
@using TRF3.SISPREC.Permissoes
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.AspNetCore.Mvc.UI.Layout
@using TRF3.SISPREC.Web.Pages.Agencias.Agencia
@using TRF3.SISPREC.Web.Menus
@model IndexModel
@inject IPageLayout PageLayout
@inject IAuthorizationService Authorization
@{
    PageLayout.Content.Title = "Agência";
    PageLayout.Content.MenuItemName = SISPRECMenus.Agencia;
}

@section scripts
{
    <abp-script src="/Pages/Agencias/index.js" />
    <script>
        $('#AgenciaFilter_MunicipioId').select2({
            theme: 'bootstrap-5',
        });
    </script>
}
@section styles
{
    <abp-style src="/Pages/Agencias/index.css" />
}
<abp-card>
    <abp-card-header>
        <abp-row class="justify-content-between align-items-center">
            <abp-column>
                <a abp-collapse-id="AgenciaCollapse" class="text-secondary">Filtrar</a>
            </abp-column>
            <abp-column class="text-end">
                @if (await Authorization.IsGrantedAsync(SISPRECPermissoes.Agencia.Gravar))
                {
                    <abp-button id="NewAgenciaButton"
                                text="Novo"
                                icon="plus"
                                button-type="Primary" />
                }
            </abp-column>
        </abp-row>
    </abp-card-header>
    <abp-card-body>
        <form abp-model="AgenciaFilter" id="AgenciaFilter" required-symbols="false">
            <abp-collapse-body id="AgenciaCollapse">
                <abp-row>
                    <abp-column size="_1">
                        <abp-input asp-for="AgenciaFilter.AgenciaId" />
                    </abp-column>
                    <abp-column size="_3">
                        <abp-input asp-for="AgenciaFilter.NomeAgencia" />
                    </abp-column>

                    <abp-column size="_2">
                        <abp-select asp-for="AgenciaFilter.BancoId" asp-items="@Model.AgenciaFilter.BancoLookupList"></abp-select>
                    </abp-column>

                    <abp-column size="_4">
                        <abp-select asp-for="@Model.AgenciaFilter.MunicipioId" asp-items="@Model.AgenciaFilter.CidadeLookupList">
                        </abp-select>
                    </abp-column>

                    <abp-column size="_2">
                        <abp-select asp-for="AgenciaFilter.Ativo"></abp-select>
                    </abp-column>
                </abp-row>
            </abp-collapse-body>
        </form>
        <abp-table striped-rows="true" id="AgenciaTable" class="nowrap" />
    </abp-card-body>
</abp-card>
